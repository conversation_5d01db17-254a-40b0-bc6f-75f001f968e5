//
//  iCloudSyncManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/3.
//

import Foundation
import CoreData
import CloudKit
import SwiftUI
import Combine

/**
 * iCloud同步管理器
 * 负责管理iCloud同步功能的开启/关闭、权限检查、数据迁移等
 */
@MainActor
class iCloudSyncManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = iCloudSyncManager()
    
    // MARK: - Published Properties
    @Published var isSyncEnabled: Bool = false
    @Published var syncStatus: SyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var isAvailable: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Constants
    private let syncEnabledKey = "icloud_sync_enabled"
    private let lastSyncDateKey = "last_sync_date"
    
    private init() {
        setupInitialState()
        observeSubscriptionChanges()
        setupSyncMonitoring()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查iCloud同步是否可用
     */
    func checkAvailability() async {
        do {
            // 检查CloudKit容器状态
            let container = CKContainer.default()
            let accountStatus = try await container.accountStatus()
            
            await MainActor.run {
                switch accountStatus {
                case .available:
                    self.isAvailable = true
                    self.errorMessage = nil
                    print("✅ iCloud账户可用")
                case .noAccount:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.no_account".localized
                    print("❌ 设备未登录iCloud账户")
                case .restricted:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.restricted".localized
                    print("❌ iCloud账户受限")
                case .couldNotDetermine:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.could_not_determine".localized
                    print("❌ 无法确定iCloud账户状态")
                case .temporarilyUnavailable:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.temporarily_unavailable".localized
                    print("❌ iCloud服务暂时不可用")
                @unknown default:
                    self.isAvailable = false
                    self.errorMessage = "icloud_sync.error.unknown".localized
                    print("❌ 未知的iCloud账户状态")
                }
            }
        } catch {
            await MainActor.run {
                self.isAvailable = false
                self.errorMessage = error.localizedDescription
                print("❌ 检查iCloud可用性失败: \(error)")
            }
        }
    }
    
    /**
     * 检查用户是否有权限使用iCloud同步
     */
    func hasPermission() -> Bool {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription else {
            return false
        }
        
        // 只有付费用户（初级会员和高级会员）可以使用iCloud同步
        return subscription.subscriptionType != "free"
    }
    
    /**
     * 尝试开启iCloud同步
     */
    func enableSync() async -> Bool {
        // 1. 检查权限
        guard hasPermission() else {
            await MainActor.run {
                self.errorMessage = "icloud_sync.error.permission_denied".localized
            }
            return false
        }
        
        // 2. 检查iCloud可用性
        await checkAvailability()
        guard isAvailable else {
            return false
        }
        
        // 3. 执行数据迁移（如果需要）
        let migrationSuccess = await performDataMigration()
        guard migrationSuccess else {
            return false
        }
        
        // 4. 启用同步
        await MainActor.run {
            self.isSyncEnabled = true
            self.syncStatus = .syncing
            self.userDefaults.set(true, forKey: self.syncEnabledKey)
            print("✅ iCloud同步已启用")
        }
        
        // 5. 初始化CloudKit容器（创建首条记录以建立schema）
        await initializeCloudKitContainer()

        // 6. 执行首次同步
        await performSync()

        return true
    }
    
    /**
     * 关闭iCloud同步
     */
    func disableSync() {
        isSyncEnabled = false
        syncStatus = .idle
        userDefaults.set(false, forKey: syncEnabledKey)

        // 切换回本地存储模式
        PersistenceController.switchToLocal()

        print("⏸️ iCloud同步已关闭，已切换到本地存储模式")
    }
    
    /**
     * 手动触发同步
     */
    func triggerManualSync() async {
        guard isSyncEnabled && isAvailable else {
            errorMessage = "icloud_sync.error.not_available".localized
            return
        }
        
        await performSync()
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置初始状态
     */
    private func setupInitialState() {
        isSyncEnabled = userDefaults.bool(forKey: syncEnabledKey)
        
        if let lastSyncTimestamp = userDefaults.object(forKey: lastSyncDateKey) as? Date {
            lastSyncDate = lastSyncTimestamp
        }
        
        // 异步检查可用性
        Task {
            await checkAvailability()
        }
    }
    
    /**
     * 监听订阅状态变化
     */
    private func observeSubscriptionChanges() {
        dataManager.$currentUser
            .sink { [weak self] user in
                guard let self = self else { return }

                // 检查订阅状态变化
                self.handleSubscriptionChange(user: user)
            }
            .store(in: &cancellables)
    }

    /**
     * 处理订阅状态变化
     */
    private func handleSubscriptionChange(user: User?) {
        guard let user = user,
              let subscription = user.subscription else {
            return
        }

        let currentType = subscription.subscriptionType
        let wasEnabled = isSyncEnabled

        // 如果从付费降级到免费用户
        if currentType == "free" && wasEnabled {
            Task {
                await handleSubscriptionDowngrade()
            }
        }
        // 如果从免费升级到付费用户
        else if currentType != "free" && !wasEnabled {
            handleSubscriptionUpgrade()
        }
    }

    /**
     * 处理订阅降级
     */
    private func handleSubscriptionDowngrade() async {
        print("⬇️ 检测到订阅降级，开始处理...")

        await MainActor.run {
            self.syncStatus = .migrating
        }

        do {
            // 1. 创建数据备份
            let backupSuccess = await createCloudDataBackup()
            guard backupSuccess else {
                throw DowngradeError.backupFailed
            }

            // 2. 关闭iCloud同步
            await MainActor.run {
                self.disableSync()
            }

            // 3. 确保本地数据完整性
            let verificationSuccess = await verifyLocalDataIntegrity()
            guard verificationSuccess else {
                throw DowngradeError.dataVerificationFailed
            }

            // 4. 显示降级通知
            await MainActor.run {
                self.showDowngradeNotification()
            }

            print("✅ 订阅降级处理完成")

        } catch {
            await MainActor.run {
                self.syncStatus = .failed
                self.errorMessage = error.localizedDescription
                print("❌ 订阅降级处理失败: \(error)")
            }
        }
    }

    /**
     * 处理订阅升级
     */
    private func handleSubscriptionUpgrade() {
        print("⬆️ 检测到订阅升级，iCloud同步功能已可用")
        errorMessage = nil

        // 可以在这里显示升级成功的提示
        // 但不自动开启同步，让用户手动选择
    }

    /**
     * 设置同步状态监控
     */
    private func setupSyncMonitoring() {
        // 监听CloudKit通知
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleRemoteChange(notification)
            }
        }

        // 定期检查同步状态
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.checkSyncHealth()
            }
            .store(in: &cancellables)
    }

    /**
     * 处理远程数据变化
     */
    private func handleRemoteChange(_ notification: Notification) {
        guard isSyncEnabled else { return }

        print("📡 检测到远程数据变化")

        // 更新同步状态
        if syncStatus != .syncing {
            syncStatus = .syncing

            // 延迟更新为成功状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                self.syncStatus = .success
                self.lastSyncDate = Date()
                self.userDefaults.set(Date(), forKey: self.lastSyncDateKey)
            }
        }
    }

    /**
     * 检查同步健康状态
     */
    private func checkSyncHealth() {
        guard isSyncEnabled && isAvailable else { return }

        // 检查是否长时间未同步
        if let lastSync = lastSyncDate,
           Date().timeIntervalSince(lastSync) > 3600 { // 1小时
            print("⚠️ 长时间未同步，建议手动同步")
        }
    }
    
    /**
     * 执行数据迁移
     */
    private func performDataMigration() async -> Bool {
        // 如果已经在使用CloudKit，无需迁移
        if dataManager.persistenceController.isCloudKitEnabled {
            return true
        }

        await MainActor.run {
            self.syncStatus = .migrating
        }

        do {
            print("🔄 开始数据迁移...")

            // 1. 备份当前数据
            let backupSuccess = await backupLocalData()
            guard backupSuccess else {
                throw MigrationError.backupFailed
            }

            // 2. 切换到CloudKit存储模式
            await MainActor.run {
                PersistenceController.switchToCloudKit()
            }

            // 3. 立即重新初始化DataManager以使用CloudKit
            await MainActor.run {
                DataManager.shared.reinitializeWithCloudKit()
            }

            // 4. 等待存储切换完成
            try await Task.sleep(nanoseconds: 2_000_000_000) // 增加等待时间

            // 5. 验证迁移结果
            let verificationSuccess = await verifyMigration()
            guard verificationSuccess else {
                throw MigrationError.verificationFailed
            }

            await MainActor.run {
                print("✅ 数据迁移完成")
            }

            return true
        } catch {
            await MainActor.run {
                self.syncStatus = .failed
                self.errorMessage = "icloud_sync.error.migration_failed".localized
                print("❌ 数据迁移失败: \(error)")
            }
            return false
        }
    }

    /**
     * 备份本地数据
     */
    private func backupLocalData() async -> Bool {
        print("📦 备份本地数据...")

        // 这里实现数据备份逻辑
        // 在实际项目中，可以将重要数据导出到临时文件

        // 模拟备份过程
        try? await Task.sleep(nanoseconds: 500_000_000)

        print("✅ 本地数据备份完成")
        return true
    }

    /**
     * 验证迁移结果
     */
    private func verifyMigration() async -> Bool {
        print("🔍 验证迁移结果...")

        // 这里实现迁移验证逻辑
        // 检查关键数据是否正确迁移到CloudKit

        // 模拟验证过程
        try? await Task.sleep(nanoseconds: 500_000_000)

        print("✅ 迁移验证完成")
        return true
    }

    // MARK: - Downgrade Handling

    /**
     * 创建云端数据备份
     */
    private func createCloudDataBackup() async -> Bool {
        print("💾 创建云端数据备份...")

        do {
            // 1. 获取当前CloudKit数据
            let container = CKContainer.default()
            let database = container.privateCloudDatabase

            // 2. 查询所有记录类型
            let recordTypes = ["User", "Member", "PointRecord", "DiaryEntry", "GlobalRule", "MemberRule", "MemberPrize"]

            for recordType in recordTypes {
                let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))

                do {
                    let (matchResults, _) = try await database.records(matching: query)
                    let records = matchResults.compactMap { try? $0.1.get() }
                    print("📦 备份 \(recordType): \(records.count) 条记录")
                } catch {
                    print("⚠️ 备份 \(recordType) 失败: \(error)")
                }
            }

            // 3. 模拟备份过程
            try await Task.sleep(nanoseconds: 1_000_000_000)

            print("✅ 云端数据备份完成")
            return true

        } catch {
            print("❌ 创建云端数据备份失败: \(error)")
            return false
        }
    }

    /**
     * 验证本地数据完整性
     */
    private func verifyLocalDataIntegrity() async -> Bool {
        print("🔍 验证本地数据完整性...")

        await MainActor.run {
            let context = self.dataManager.persistenceController.container.viewContext

            do {
                // 检查关键数据是否存在
                let userRequest: NSFetchRequest<User> = User.fetchRequest()
                let users = try context.fetch(userRequest)

                let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
                let members = try context.fetch(memberRequest)

                print("✅ 本地数据验证完成 - 用户: \(users.count), 成员: \(members.count)")

            } catch {
                print("❌ 本地数据验证失败: \(error)")
            }
        }

        return true
    }

    /**
     * 显示降级通知
     */
    private func showDowngradeNotification() {
        // 发送通知给UI层显示降级提示
        NotificationCenter.default.post(
            name: .subscriptionDowngraded,
            object: nil,
            userInfo: [
                "message": "subscription_downgrade.notification.message".localized,
                "action": "subscription_downgrade.notification.action".localized
            ]
        )

        errorMessage = "icloud_sync.error.subscription_expired".localized
    }
    
    /**
     * 执行同步操作
     */
    private func performSync() async {
        await MainActor.run {
            self.syncStatus = .syncing
        }

        do {
            print("🔄 开始同步数据...")
            print("📊 当前用户: \(dataManager.currentUser?.nickname ?? "无")")
            print("📊 成员数量: \(dataManager.members.count)")
            print("📊 CloudKit状态: \(dataManager.persistenceController.isCloudKitEnabled ? "已启用" : "未启用")")

            // 触发Core Data保存，这会自动启动CloudKit同步
            await MainActor.run {
                self.dataManager.persistenceController.save()
            }

            // 等待同步完成
            try await Task.sleep(nanoseconds: 2_000_000_000) // 增加等待时间

            // 验证同步状态
            await verifyCloudKitSync()

            await MainActor.run {
                self.syncStatus = .success
                self.lastSyncDate = Date()
                self.userDefaults.set(Date(), forKey: self.lastSyncDateKey)
                self.errorMessage = nil
                print("✅ 数据同步完成")
            }
        } catch {
            await MainActor.run {
                self.syncStatus = .failed
                self.errorMessage = error.localizedDescription
                print("❌ 数据同步失败: \(error)")
            }
        }
    }

    /**
     * 验证CloudKit同步状态
     */
    private func verifyCloudKitSync() async {
        do {
            let container = CKContainer.default()
            let database = container.privateCloudDatabase

            // CloudKit记录类型通常是Core Data实体名称加上"CD"前缀
            let recordTypes = ["CDUser", "CDMember", "CDSubscription", "CDGlobalRule"]

            print("☁️ CloudKit同步验证:")

            for recordType in recordTypes {
                do {
                    let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))
                    let (results, _) = try await database.records(matching: query)
                    let records = results.compactMap { try? $0.1.get() }

                    print("   - \(recordType): \(records.count) 条记录")

                    // 如果是用户记录，打印详细信息
                    if recordType == "CDUser" {
                        for record in records {
                            let nickname = record["nickname"] as? String ?? "未知"
                            let appleUserID = record["appleUserID"] as? String ?? "无"
                            print("     - 用户: \(nickname) (Apple ID: \(appleUserID))")
                        }
                    }

                } catch {
                    print("   - \(recordType): 查询失败 - \(error.localizedDescription)")

                    // 如果是"Unknown Item"错误，说明记录类型不存在
                    if let ckError = error as? CKError, ckError.code == .unknownItem {
                        print("     ⚠️ 记录类型 \(recordType) 不存在，可能需要首次创建数据")
                    }
                }
            }

        } catch {
            print("⚠️ CloudKit同步验证失败: \(error)")
        }
    }

    /**
     * 初始化CloudKit容器
     * 通过创建首条记录来建立CloudKit schema
     */
    private func initializeCloudKitContainer() async {
        print("🔧 初始化CloudKit容器...")

        guard let currentUser = dataManager.currentUser else {
            print("❌ 无当前用户，跳过CloudKit初始化")
            return
        }

        await MainActor.run {
            let context = dataManager.persistenceController.container.viewContext

            // 确保用户有必要的属性
            if currentUser.id == nil {
                currentUser.id = UUID()
            }
            if currentUser.createdAt == nil {
                currentUser.createdAt = Date()
            }

            // 更新创建时间，强制触发CloudKit同步
            currentUser.createdAt = Date()

            // 如果用户没有订阅信息，创建一个
            if currentUser.subscription == nil {
                let subscription = Subscription(context: context)
                subscription.id = UUID()
                subscription.subscriptionType = "premium" // 确保是高级会员
                subscription.isActive = true
                subscription.startDate = Date()
                subscription.createdAt = Date()
                subscription.updatedAt = Date()
                subscription.user = currentUser
                currentUser.subscription = subscription

                print("📝 创建订阅信息: \(subscription.subscriptionType ?? "unknown")")
            }

            // 强制保存到Core Data，触发CloudKit同步
            do {
                try context.save()
                print("💾 用户数据已保存到Core Data")
            } catch {
                print("❌ 保存用户数据失败: \(error)")
            }
        }

        // 等待CloudKit schema建立
        try? await Task.sleep(nanoseconds: 5_000_000_000) // 增加到5秒

        print("✅ CloudKit容器初始化完成")
    }
}

// MARK: - Sync Status Enum

enum SyncStatus {
    case idle
    case syncing
    case migrating
    case success
    case failed

    var displayText: String {
        switch self {
        case .idle:
            return "icloud_sync.status.idle".localized
        case .syncing:
            return "icloud_sync.status.syncing".localized
        case .migrating:
            return "icloud_sync.status.migrating".localized
        case .success:
            return "icloud_sync.status.success".localized
        case .failed:
            return "icloud_sync.status.failed".localized
        }
    }

    var iconName: String {
        switch self {
        case .idle:
            return "icloud"
        case .syncing, .migrating:
            return "icloud.and.arrow.up"
        case .success:
            return "icloud.and.arrow.up.fill"
        case .failed:
            return "icloud.slash"
        }
    }
}

// MARK: - Migration Error Enum

enum MigrationError: LocalizedError {
    case backupFailed
    case verificationFailed
    case dataCorrupted

    var errorDescription: String? {
        switch self {
        case .backupFailed:
            return "数据备份失败"
        case .verificationFailed:
            return "迁移验证失败"
        case .dataCorrupted:
            return "数据损坏"
        }
    }
}

// MARK: - Downgrade Error Enum

enum DowngradeError: LocalizedError {
    case backupFailed
    case dataVerificationFailed
    case syncDisableFailed

    var errorDescription: String? {
        switch self {
        case .backupFailed:
            return "subscription_downgrade.error.backup_failed".localized
        case .dataVerificationFailed:
            return "subscription_downgrade.error.verification_failed".localized
        case .syncDisableFailed:
            return "subscription_downgrade.error.disable_failed".localized
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let subscriptionDowngraded = Notification.Name("subscriptionDowngraded")
    static let subscriptionUpgraded = Notification.Name("subscriptionUpgraded")
}
